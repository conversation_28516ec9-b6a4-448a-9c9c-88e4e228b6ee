// Touch Scroll Test for EmojiPickerPreview
// This component tests manual touch scrolling functionality

import React, { useState, useRef } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Alert,
    ScrollView,
    Platform,
} from 'react-native';
import { useTheme } from '@context/theme';
import EmojiPickerPreview from './picker/preview';
import type { SelectedEmoji } from '@components/post_draft/emoji_preview';

const TouchScrollTest = () => {
    const theme = useTheme();
    const [selectedEmojis, setSelectedEmojis] = useState<SelectedEmoji[]>([]);
    const [testResults, setTestResults] = useState<string[]>([]);
    const scrollViewRef = useRef<ScrollView>(null);

    const generateTestEmojis = (count: number): SelectedEmoji[] => {
        const emojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍'];
        return Array.from({ length: count }, (_, i) => ({
            id: `test_emoji_${i}`,
            character: emojis[i % emojis.length],
            name: `test_${i}`,
        }));
    };

    const addTestResult = (result: string) => {
        setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
    };

    const runTouchScrollTest = () => {
        setTestResults([]);
        addTestResult('🧪 Starting Touch Scroll Test');
        
        // Generate enough emojis to require scrolling
        const testEmojis = generateTestEmojis(10);
        setSelectedEmojis(testEmojis);
        
        addTestResult(`✅ Generated ${testEmojis.length} test emojis`);
        addTestResult('👆 Now try to manually scroll the emoji preview horizontally');
        addTestResult('📱 Look for "Manual Scroll Begin Drag" logs in console');
        
        setTimeout(() => {
            Alert.alert(
                'Touch Scroll Test',
                'Test emojis have been added. Try to scroll the emoji preview horizontally with your finger. Check the console logs for "Manual Scroll Begin Drag" messages.',
                [
                    { text: 'Clear Test', onPress: clearTest },
                    { text: 'OK' }
                ]
            );
        }, 1000);
    };

    const clearTest = () => {
        setSelectedEmojis([]);
        setTestResults([]);
        addTestResult('🧹 Test cleared');
    };

    const handleRemoveEmoji = (id: string) => {
        setSelectedEmojis(prev => prev.filter(emoji => emoji.id !== id));
        addTestResult(`🗑️ Removed emoji with id: ${id}`);
    };

    const handleDone = () => {
        addTestResult('✅ Done button pressed');
        clearTest();
    };

    const styles = StyleSheet.create({
        container: {
            flex: 1,
            padding: 16,
            backgroundColor: theme.centerChannelBg,
        },
        title: {
            fontSize: 18,
            fontWeight: 'bold',
            color: theme.centerChannelColor,
            marginBottom: 16,
            textAlign: 'center',
        },
        instructions: {
            fontSize: 14,
            color: theme.centerChannelColor,
            marginBottom: 16,
            lineHeight: 20,
        },
        buttonContainer: {
            flexDirection: 'row',
            justifyContent: 'space-around',
            marginBottom: 16,
        },
        button: {
            backgroundColor: theme.buttonBg,
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderRadius: 8,
        },
        buttonText: {
            color: theme.buttonColor,
            fontWeight: '600',
        },
        previewContainer: {
            marginVertical: 16,
        },
        resultsContainer: {
            flex: 1,
            marginTop: 16,
        },
        resultsTitle: {
            fontSize: 16,
            fontWeight: '600',
            color: theme.centerChannelColor,
            marginBottom: 8,
        },
        resultItem: {
            fontSize: 12,
            color: theme.centerChannelColor,
            marginBottom: 4,
            fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
        },
    });

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Touch Scroll Test</Text>
            
            <Text style={styles.instructions}>
                This test validates that the EmojiPickerPreview component responds to manual touch gestures for horizontal scrolling.
                {'\n\n'}Steps:
                {'\n'}1. Tap "Run Touch Test" to generate test emojis
                {'\n'}2. Try to scroll the emoji preview horizontally with your finger
                {'\n'}3. Check console logs for "Manual Scroll Begin Drag" messages
                {'\n'}4. Successful manual scrolling indicates the fix is working
            </Text>

            <View style={styles.buttonContainer}>
                <TouchableOpacity style={styles.button} onPress={runTouchScrollTest}>
                    <Text style={styles.buttonText}>Run Touch Test</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.button} onPress={clearTest}>
                    <Text style={styles.buttonText}>Clear Test</Text>
                </TouchableOpacity>
            </View>

            {selectedEmojis.length > 0 && (
                <View style={styles.previewContainer}>
                    <EmojiPickerPreview
                        selectedEmojis={selectedEmojis}
                        onRemoveEmoji={handleRemoveEmoji}
                        onDone={handleDone}
                        testID="touch_scroll_test_preview"
                    />
                </View>
            )}

            <View style={styles.resultsContainer}>
                <Text style={styles.resultsTitle}>Test Results:</Text>
                <ScrollView ref={scrollViewRef} style={{ flex: 1 }}>
                    {testResults.map((result, index) => (
                        <Text key={index} style={styles.resultItem}>
                            {result}
                        </Text>
                    ))}
                </ScrollView>
            </View>
        </View>
    );
};

export default TouchScrollTest;
