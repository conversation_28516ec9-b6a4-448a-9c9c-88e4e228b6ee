// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useRef, useEffect } from "react";
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Platform,
    Animated,
} from "react-native";
import { ScrollView } from "react-native-gesture-handler";



import CompassIcon from "@components/compass_icon";
import { useTheme } from "@context/theme";
import { changeOpacity, makeStyleSheetFromTheme } from "@utils/theme";
import { typography } from "@utils/typography";

import type { SelectedEmoji } from "@components/post_draft/emoji_preview";

type Props = {
    selectedEmojis: SelectedEmoji[];
    onRemoveEmoji: (id: string) => void;
    onDone: () => void;
    testID?: string;
};

const getStyleSheet = makeStyleSheetFromTheme((theme) => {
    return StyleSheet.create({
        container: {
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.04),
            borderRadius: 8,
            borderWidth: 1,
            borderColor: changeOpacity(theme.centerChannelColor, 0.16),
            paddingHorizontal: 8,
            paddingVertical: 5,
            maxHeight: 100,
            minHeight: 100,
            marginHorizontal: 8,
            marginVertical: 8,
            // ...Platform.select({
            //     ios: {
            //         shadowColor: '#000',
            //         shadowOffset: { width: 0, height: 2 },
            //         shadowOpacity: 0.1,
            //         shadowRadius: 6,
            //     },
            //     android: {
            //         elevation: 3,
            //     },
            // }),
        },
        header: {
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 12,
        },
        title: {
            color: theme.centerChannelColor,
            ...typography('Heading', 100, 'SemiBold'),
        },
        doneButton: {
            backgroundColor: theme.buttonBg,
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderRadius: 8,
            minWidth: 64,
            height: 32,
            alignItems: "center",
            justifyContent: "center",
            ...Platform.select({
                ios: {
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 1 },
                    shadowOpacity: 0.2,
                    shadowRadius: 3,
                },
                android: {
                    elevation: 2,
                },
            }),
        },
        doneButtonText: {
            color: theme.buttonColor,
            ...typography('Body', 75, 'SemiBold'),
        },
        scrollContainer: {
            flex: 1,
            borderRadius: 8,
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.02),
        },
        scrollContent: {
            flexDirection: "row",
            alignItems: "center",
            paddingVertical: 8,
            paddingHorizontal: 8,
            minHeight: 10,
        },
        emptyText: {
            color: changeOpacity(theme.centerChannelColor, 0.56),
            textAlign: "center",
            flex: 1,
            paddingVertical: 16,
            ...typography('Body', 75, 'Regular'),
        },
        emojiItem: {
            flexDirection: "row",
            alignItems: "center",
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.08),
            borderRadius: 16,
            paddingHorizontal: 8,
            paddingVertical: 6,
            marginRight: 8,
            minWidth: 50,
            height: 32,
            flexShrink: 0,
        },
        emojiCharacter: {
            fontSize: 16,
            marginRight: 4,
            lineHeight: 20,
        },
        removeButton: {
            padding: 2,
            borderRadius: 8,
            minWidth: 16,
            minHeight: 16,
            alignItems: "center",
            justifyContent: "center",
        },
        removeIcon: {
            color: changeOpacity(theme.centerChannelColor, 0.56),
        },

    });
});

const EmojiPickerPreview = ({
    selectedEmojis,
    onRemoveEmoji,
    onDone,
    testID = "emoji_picker_preview",
}: Props) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);
    const scrollViewRef = useRef<ScrollView>(null);

    // Animation values
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const scaleAnim = useRef(new Animated.Value(0.95)).current;

    // Animate in when component mounts
    useEffect(() => {
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
                toValue: 1,
                tension: 100,
                friction: 8,
                useNativeDriver: true,
            }),
        ]).start();
    }, [fadeAnim, scaleAnim]);

    const handleRemoveEmoji = useCallback(
        (id: string) => {
            // Add a subtle scale animation when removing
            Animated.sequence([
                Animated.timing(scaleAnim, {
                    toValue: 0.98,
                    duration: 100,
                    useNativeDriver: true,
                }),
                Animated.timing(scaleAnim, {
                    toValue: 1,
                    duration: 100,
                    useNativeDriver: true,
                }),
            ]).start();
            onRemoveEmoji(id);
        },
        [onRemoveEmoji, scaleAnim]
    );

    const handleDone = useCallback(() => {
        // Add a subtle press animation
        Animated.sequence([
            Animated.timing(scaleAnim, {
                toValue: 0.96,
                duration: 100,
                useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
                toValue: 1,
                duration: 100,
                useNativeDriver: true,
            }),
        ]).start(() => {
            onDone();
        });
    }, [onDone, scaleAnim]);







    const scrollConfig = {
        ios: {
            nestedScrollEnabled: true,
            scrollEventThrottle: 16,
            decelerationRate: "fast" as const,
            bounces: true,
            alwaysBounceHorizontal: true,
            alwaysBounceVertical: false,
        },
        android: {
            nestedScrollEnabled: true,
            scrollEventThrottle: 16,
            decelerationRate: "normal" as const,
            bounces: false,
            alwaysBounceHorizontal: false,
            alwaysBounceVertical: false,
        },
    };

    const currentScrollConfig =
        Platform.OS === "ios" ? scrollConfig.ios : scrollConfig.android;

    // Simplified approach: Focus on ScrollView configuration instead of PanResponder
    // The issue might be that PanResponder interferes with ScrollView's native touch handling

    // if (selectedEmojis.length === 0) {
    //     return (
    //         <Animated.View
    //             style={[
    //                 styles.container,
    //                 {
    //                     opacity: fadeAnim,
    //                     transform: [{ scale: scaleAnim }]
    //                 }
    //             ]}
    //             testID={`${testID}.container`}
    //         >
    //             <View style={styles.header}>
    //                 <Text style={styles.title}>Selected (0)</Text>
    //             </View>
    //             <Text style={styles.emptyText}>Tap emojis to select them</Text>
    //         </Animated.View>
    //     );
    // }

    // return (
        // <Animated.View
        //     style={[
        //         styles.container,
        //         {
        //             opacity: fadeAnim,
        //             transform: [{ scale: scaleAnim }]
        //         }
        //     ]}
        //     testID={`${testID}.container`}
        // >
        //     <View style={styles.header}>
        //         <Text style={styles.title}>
        //             Selected ({selectedEmojis.length})
        //         </Text>
        //         <TouchableOpacity
        //             style={styles.doneButton}
        //             onPress={handleDone}
        //             testID={`${testID}.done_button`}
        //             activeOpacity={0.8}
        //             delayPressIn={0}
        //             delayPressOut={50}
        //         >
        //             <Text style={styles.doneButtonText}>Done</Text>
        //         </TouchableOpacity>

        //     </View>
        //     <View style={styles.scrollContainer}>
        //         <ScrollView
        //             ref={scrollViewRef}
        //             horizontal
        //             showsHorizontalScrollIndicator={true}
        //             contentContainerStyle={styles.scrollContent}
        //             testID={`${testID}.scroll_view`}
        //             bounces={currentScrollConfig.bounces}
        //             scrollEventThrottle={
        //                 currentScrollConfig.scrollEventThrottle
        //             }
        //             decelerationRate={currentScrollConfig.decelerationRate}
        //             nestedScrollEnabled={
        //                 currentScrollConfig.nestedScrollEnabled
        //             }
        //             alwaysBounceHorizontal={
        //                 currentScrollConfig.alwaysBounceHorizontal
        //             }
        //             alwaysBounceVertical={
        //                 currentScrollConfig.alwaysBounceVertical
        //             }
        //             removeClippedSubviews={false}
        //             keyboardShouldPersistTaps="handled"
        //             directionalLockEnabled={true}
        //             scrollEnabled={true}
        //             pagingEnabled={false}
        //             disableIntervalMomentum={false}
        //             showsVerticalScrollIndicator={false}
        //             shouldCancelWhenOutside={false}

        //         >
        //             {selectedEmojis.map((emoji) => (
        //                 <View
        //                     key={emoji.id}
        //                     style={styles.emojiItem}
        //                     testID={`${testID}.emoji_item.${emoji.id}`}
        //                 >
        //                     <Text style={styles.emojiCharacter}>
        //                         {emoji.character}
        //                     </Text>
        //                     <TouchableOpacity
        //                         style={styles.removeButton}
        //                         onPress={() => handleRemoveEmoji(emoji.id)}
        //                         testID={`${testID}.remove_button.${emoji.id}`}
        //                         hitSlop={{
        //                             top: 8,
        //                             bottom: 8,
        //                             left: 8,
        //                             right: 8,
        //                         }}
        //                         activeOpacity={0.6}
        //                         delayPressIn={0}
        //                         delayPressOut={50}
        //                     >
        //                         <CompassIcon
        //                             name="close"
        //                             size={10}
        //                             style={styles.removeIcon}
        //                         />
        //                     </TouchableOpacity>
        //                 </View>
        //             ))}
        //         </ScrollView>

        //     </View>
        // </Animated.View>
    // );
};

export default EmojiPickerPreview;
